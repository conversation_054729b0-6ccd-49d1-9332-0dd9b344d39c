["test_validation.py::TestPatchSetValidation::test_patchset_apply_with_validation", "test_validation.py::TestPatchSetValidation::test_patchset_apply_with_validation_disabled", "test_validation.py::TestPatchSetValidation::test_patchset_validate_method", "test_validation.py::TestPatchValidator::test_absolute_path_warning", "test_validation.py::TestPatchValidator::test_empty_patchset_validation", "test_validation.py::TestPatchValidator::test_invalid_hunk_header_validation", "test_validation.py::TestPatchValidator::test_invalid_line_prefix_validation", "test_validation.py::TestPatchValidator::test_line_count_mismatch_validation", "test_validation.py::TestPatchValidator::test_mixed_patch_types_suggestion", "test_validation.py::TestPatchValidator::test_overlapping_hunks_warning", "test_validation.py::TestPatchValidator::test_parent_directory_warning", "test_validation.py::TestPatchValidator::test_trailing_whitespace_warning", "test_validation.py::TestPatchValidator::test_valid_patch_validation", "test_validation.py::TestValidateBeforeApply::test_validate_before_apply_with_existing_files", "test_validation.py::TestValidateBeforeApply::test_validate_before_apply_with_missing_files"]