{"test_validation.py::TestPatchValidator::test_valid_patch_validation": true, "test_validation.py::TestPatchValidator::test_invalid_hunk_header_validation": true, "test_validation.py::TestPatchValidator::test_line_count_mismatch_validation": true, "test_validation.py::TestValidateBeforeApply::test_validate_before_apply_with_existing_files": true, "test_validation.py::TestValidateBeforeApply::test_validate_before_apply_with_missing_files": true, "test_validation.py::TestPatchSetValidation::test_patchset_validate_method": true, "test_validation.py::TestPatchSetValidation::test_patchset_apply_with_validation": true, "test_validation.py::TestPatchSetValidation::test_patchset_apply_with_validation_disabled": true}